<?php
require_once '../includes/session_manager.php';
require '../koneksi.php';

// Validasi session client
check_session_auth('client');

$page_title = "Progress Proyek";
include 'includes/header/header.php';
?>

<?php include 'includes/sidebar/sidebar.php'; ?>

<div id="content-wrapper" class="d-flex flex-column">
    <div id="content">
        <?php include 'includes/topbar/topbar.php'; ?>
        <div class="container-fluid">

            <div class="d-sm-flex align-items-center justify-content-between mb-4">
                <h1 class="h3 mb-0 text-gray-800">Progress Proyek Anda</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0 bg-transparent p-0">
                        <li class="breadcrumb-item"><a href="client.php">Dashboard</a></li>
                        <li class="breadcrumb-item active">Progress Proyek</li>
                    </ol>
                </nav>
            </div>

            <div class="alert alert-info">
                <i class="fas fa-info-circle mr-2"></i>
                Timeline menampilkan progress tugas proyek Anda yang telah disetujui.
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-line mr-2"></i>Ringkasan Progress
                    </h6>
                </div>
                <div class="card-body">
                    <?php
                    $client_id = $_SESSION['id_client'];
                    function get_task_count($koneksi, $client_id, $status = null) {
                        if ($status) {
                            $sql = "SELECT COUNT(*) FROM tugas_proyek WHERE client_id = ? AND status_verifikasi = 'approved' AND status = ?";
                            $stmt = mysqli_prepare($koneksi, $sql);
                            mysqli_stmt_bind_param($stmt, "is", $client_id, $status);
                        } else {
                            $sql = "SELECT COUNT(*) FROM tugas_proyek WHERE client_id = ? AND status_verifikasi = 'approved'";
                            $stmt = mysqli_prepare($koneksi, $sql);
                            mysqli_stmt_bind_param($stmt, "i", $client_id);
                        }
                        mysqli_stmt_execute($stmt);
                        $result = mysqli_stmt_get_result($stmt);
                        return mysqli_fetch_row($result)[0] ?? 0;
                    }
                    $total_tugas = get_task_count($koneksi, $client_id);
                    $tugas_selesai = get_task_count($koneksi, $client_id, 'selesai');
                    $tugas_proses = get_task_count($koneksi, $client_id, 'proses');
                    $tugas_batal = get_task_count($koneksi, $client_id, 'batal');
                    $progress_percentage = $total_tugas > 0 ? ($tugas_selesai / $total_tugas) * 100 : 0;
                    ?>
                    <div class="row">
                        <div class="col-md-8">
                            <h5 class="mb-3">Progress Keseluruhan: <?php echo round($progress_percentage, 1); ?>%</h5>
                            <div class="progress mb-3" style="height: 25px;">
                                <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $progress_percentage; ?>%" aria-valuenow="<?php echo $progress_percentage; ?>" aria-valuemin="0" aria-valuemax="100">
                                    <?php echo round($progress_percentage, 1); ?>%
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="text-success font-weight-bold h4"><?php echo $tugas_selesai; ?></div>
                                    <small class="text-muted">Selesai</small>
                                </div>
                                <div class="col-4">
                                    <div class="text-warning font-weight-bold h4"><?php echo $tugas_proses; ?></div>
                                    <small class="text-muted">Proses</small>
                                </div>
                                <div class="col-4">
                                    <div class="text-danger font-weight-bold h4"><?php echo $tugas_batal; ?></div>
                                    <small class="text-muted">Batal</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary"><i class="fas fa-tasks mr-2"></i>Timeline Proyek Anda</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="thead-light">
                                <tr>
                                    <th>Nama Tugas</th>
                                    <th class="text-center">Tanggal Mulai</th>
                                    <th class="text-center">Tanggal Selesai</th>
                                    <th class="text-center">Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $stmt = mysqli_prepare($koneksi, "SELECT * FROM tugas_proyek WHERE client_id = ? AND status_verifikasi = 'approved' ORDER BY tgl_mulai ASC");
                                mysqli_stmt_bind_param($stmt, "i", $client_id);
                                mysqli_stmt_execute($stmt);
                                $result_tugas = mysqli_stmt_get_result($stmt);
                                
                                if (mysqli_num_rows($result_tugas) > 0):
                                    while ($data = mysqli_fetch_assoc($result_tugas)):
                                        $status_class = 'secondary';
                                        if ($data['status'] == 'proses') { $status_class = 'warning'; } 
                                        elseif ($data['status'] == 'selesai') { $status_class = 'success'; } 
                                        elseif ($data['status'] == 'batal') { $status_class = 'danger'; }
                                ?>
                                    <tr>
                                        <td class="align-middle">
                                            <strong><?php echo htmlspecialchars($data['nama_kegiatan']); ?></strong>
                                                <p class="text-muted mb-0" style="font-size: 0.85rem;"><?php echo htmlspecialchars($data['deskripsi']); ?></p>
                                        </td>
                                        <td class="text-center align-middle">
                                            <?php echo date('d M Y', strtotime($data['tgl_mulai'])); ?>
                                        </td>
                                        <td class="text-center align-middle">
                                            <?php echo date('d M Y', strtotime($data['tgl_selesai'])); ?>
                                        </td>
                                        <td class="text-center align-middle">
                                            <span class="badge badge-<?php echo $status_class; ?>"><?php echo ucfirst($data['status']); ?></span>
                                        </td>
                                    </tr>
                                <?php 
                                    endwhile;
                                else: 
                                ?>
                                    <tr>
                                        <td colspan="4" class="text-center py-5">
                                            <i class="fas fa-inbox fa-3x text-gray-300 mb-3"></i>
                                            <h5 class="text-gray-600">Belum Ada Tugas untuk Proyek Anda.</h5>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            </div>
    </div>
    <?php include 'includes/footer/footer.php'; ?>
</div>