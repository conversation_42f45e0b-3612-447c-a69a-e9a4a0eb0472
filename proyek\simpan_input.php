<?php
require_once '../includes/session_manager.php';
// 📝 PERBAIKAN 1: Hak akses diubah menjadi 'admin'
// <PERSON>a hanya admin yang menekan tombol "Simpan" dari form input.
check_session_auth('admin'); 
require_once '../koneksi.php';

// Cek apakah request datang dari metode POST
if ($_SERVER["REQUEST_METHOD"] == "POST") {

    // Ambil semua data dari form
    $client_id = $_POST['client_id'];
    $nama_kegiatan = trim($_POST['nama_kegiatan']);
    $deskripsi = trim($_POST['deskripsi']);
    $tgl_mulai = $_POST['tgl_mulai'];
    $tgl_selesai = $_POST['tgl_selesai'];

    // Validasi input
    if (empty($client_id) || empty($nama_kegiatan) || empty($deskripsi) || empty($tgl_mulai) || empty($tgl_selesai)) {
        $_SESSION['error_message'] = "Semua kolom wajib diisi!";
        // Redirect kembali ke form input di folder admin
        header("Location: ../admin/input_tugas.php"); 
        exit();
    }

    // Siapkan perintah SQL INSERT yang aman
    $sql = "INSERT INTO tugas_proyek (client_id, nama_kegiatan, deskripsi, tgl_mulai, tgl_selesai) VALUES (?, ?, ?, ?, ?)";
    $stmt = mysqli_prepare($koneksi, $sql);
    
    // Bind parameter ke statement
    mysqli_stmt_bind_param($stmt, "issss", $client_id, $nama_kegiatan, $deskripsi, $tgl_mulai, $tgl_selesai);

    // Eksekusi dan siapkan notifikasi
    if (mysqli_stmt_execute($stmt)) {
        $_SESSION['success_message'] = "Tugas baru berhasil ditambahkan!";
    } else {
        $_SESSION['error_message'] = "Gagal menyimpan tugas.";
    }

    mysqli_stmt_close($stmt);
    mysqli_close($koneksi);

    // 📝 PERBAIKAN 2: Arahkan kembali ke halaman input tugas di folder admin
    header("Location: ../admin/input_tugas.php");
    exit();

} else {
    // Jika file diakses langsung, redirect ke halaman input tugas admin
    header("Location: ../admin/input_tugas.php");
    exit();
}
?>