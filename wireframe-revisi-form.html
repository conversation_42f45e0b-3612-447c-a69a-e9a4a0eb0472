<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>Ajukan Revisi - Wireframe</title>
    <style>
        /* WIREFRAME STYLES - Low Fidelity */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            background-color: #ffffff;
            color: #333333;
            line-height: 1.4;
        }
        
        /* Layout Structure */
        #wrapper {
            display: flex;
            min-height: 100vh;
            border: 3px solid #000;
        }
        
        /* Sidebar - Wireframe Style */
        .sidebar {
            width: 200px;
            background: #f0f0f0;
            border-right: 3px solid #000;
            padding: 10px;
            position: fixed;
            height: 100vh;
        }
        
        .sidebar-brand {
            text-align: center;
            padding: 15px 0;
            border: 2px solid #000;
            margin-bottom: 15px;
            background: #e0e0e0;
            font-weight: bold;
        }
        
        .nav-item {
            list-style: none;
            margin: 5px 0;
        }
        
        .nav-link {
            display: block;
            padding: 10px;
            border: 1px solid #666;
            text-decoration: none;
            color: #333;
            background: #f8f8f8;
        }
        
        .nav-item.active .nav-link {
            background: #d0d0d0;
            border: 2px solid #000;
            font-weight: bold;
        }
        
        /* Content Area */
        #content-wrapper {
            flex: 1;
            margin-left: 200px;
            display: flex;
            flex-direction: column;
        }
        
        /* Topbar - Wireframe */
        .topbar {
            background: #f5f5f5;
            padding: 15px;
            border-bottom: 3px solid #000;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        /* Main Content */
        .container-fluid {
            padding: 20px;
            flex: 1;
        }
        
        .page-heading {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #000;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: bold;
        }
        
        .breadcrumb {
            font-size: 14px;
            color: #666;
            border: 1px dashed #999;
            padding: 5px;
        }
        
        /* Wireframe Components */
        .wireframe-box {
            border: 2px solid #000;
            background: #f9f9f9;
            margin-bottom: 20px;
            padding: 15px;
        }
        
        .wireframe-header {
            background: #e0e0e0;
            padding: 10px;
            border: 1px solid #000;
            font-weight: bold;
            margin: -15px -15px 15px -15px;
        }
        
        .alert-wireframe {
            border: 2px dashed #666;
            background: #f0f0f0;
            padding: 15px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        /* Form Elements */
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            font-weight: bold;
            margin-bottom: 5px;
            display: block;
            border-bottom: 1px solid #ccc;
            padding-bottom: 3px;
        }
        
        .form-control-wireframe {
            width: 100%;
            padding: 10px;
            border: 2px solid #000;
            background: #f8f8f8;
            font-size: 14px;
        }
        
        .form-text {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
            border: 1px dashed #ccc;
            padding: 3px;
        }
        
        .textarea-wireframe {
            min-height: 80px;
            resize: vertical;
        }
        
        /* File Upload Area */
        .file-upload-wireframe {
            border: 3px dashed #000;
            background: #f0f0f0;
            padding: 30px;
            text-align: center;
            margin: 15px 0;
        }
        
        .file-upload-icon {
            font-size: 48px;
            margin-bottom: 15px;
            border: 2px solid #666;
            padding: 15px;
            background: #e0e0e0;
            display: inline-block;
        }
        
        /* Buttons */
        .button-wireframe {
            display: inline-block;
            padding: 12px 20px;
            border: 2px solid #000;
            background: #f0f0f0;
            text-decoration: none;
            color: #000;
            margin-right: 10px;
            font-size: 14px;
            font-weight: bold;
        }
        
        .button-primary {
            background: #d0d0d0;
        }
        
        .button-large {
            padding: 15px 25px;
            font-size: 16px;
        }
        
        /* Grid Layout */
        .row {
            display: flex;
            flex-wrap: wrap;
            margin: -10px;
        }
        
        .col-centered {
            padding: 10px;
            flex: 0 0 75%;
            margin: 0 auto;
        }
        
        /* Footer */
        .footer {
            background: #f0f0f0;
            padding: 15px;
            text-align: center;
            border-top: 3px solid #000;
            margin-top: auto;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            #content-wrapper {
                margin-left: 0;
            }
            
            .col-centered {
                flex: 0 0 100%;
            }
        }
    </style>
</head>
<body>
    <div id="wrapper">
        <!-- Sidebar Wireframe -->
        <ul class="sidebar">
            <div class="sidebar-brand">
                [LOGO]<br>Client Portal
            </div>
            
            <li class="nav-item">
                <a class="nav-link" href="#dashboard">
                    [📊] Dashboard
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="#progress">
                    [📋] Progress Proyek
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="#files">
                    [🖼️] File Desain
                </a>
            </li>
            
            <li class="nav-item active">
                <a class="nav-link" href="#revision">
                    [✏️] Ajukan Revisi
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="#rab">
                    [🧮] Lihat RAB
                </a>
            </li>
        </ul>
        
        <!-- Content Wrapper -->
        <div id="content-wrapper">
            <!-- Topbar Wireframe -->
            <nav class="topbar">
                <div>
                    [☰ Menu Toggle]
                </div>
                <div>
                    [🔔 Notifications (3)] [👤 vickymosafan ▼]
                </div>
            </nav>
            
            <!-- Main Content -->
            <div class="container-fluid">
                <!-- Page Heading -->
                <div class="page-heading">
                    <h1 class="page-title">Ajukan Revisi</h1>
                    <div class="breadcrumb">
                        Dashboard > Ajukan Revisi
                    </div>
                </div>
                
                <!-- Quota Info Alert -->
                <div class="alert-wireframe">
                    <div style="margin-right: 15px; font-size: 32px; border: 1px solid #000; padding: 10px;">[ℹ️]</div>
                    <div>
                        <h3>Quota Revisi Harian</h3>
                        <p>Anda telah menggunakan <strong>1</strong> dari <strong>4</strong> quota revisi hari ini. Sisa quota: <strong>3</strong> revisi.</p>
                    </div>
                </div>
                
                <!-- Form Ajukan Revisi -->
                <div class="row">
                    <div class="col-centered">
                        <div class="wireframe-box">
                            <div class="wireframe-header">
                                [✏️] Form Ajukan Revisi
                            </div>
                            <form>
                                <div class="form-group">
                                    <label class="form-label">Jenis Item</label>
                                    <select class="form-control-wireframe">
                                        <option>[Dropdown] Pilih Jenis Item</option>
                                        <option>File Desain</option>
                                        <option>Tugas Proyek</option>
                                    </select>
                                    <div class="form-text">Pilih jenis item yang ingin direvisi</div>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">Item yang Direvisi</label>
                                    <select class="form-control-wireframe">
                                        <option>[Dropdown] Pilih item spesifik</option>
                                    </select>
                                    <div class="form-text">Pilih item spesifik yang ingin direvisi</div>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">Jenis Revisi</label>
                                    <select class="form-control-wireframe">
                                        <option>[Dropdown] Pilih Jenis Revisi</option>
                                        <option>Minor (Perubahan Kecil)</option>
                                        <option>Major (Perubahan Besar)</option>
                                        <option>Critical (Perubahan Mendesak)</option>
                                    </select>
                                    <div class="form-text">Tentukan tingkat urgensi revisi</div>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">Deskripsi Revisi</label>
                                    <textarea class="form-control-wireframe textarea-wireframe" placeholder="[Text Area] Jelaskan secara detail revisi yang diinginkan..."></textarea>
                                    <div class="form-text">Berikan penjelasan yang jelas dan detail tentang revisi yang diinginkan</div>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">File Pendukung (Opsional)</label>
                                    <div class="file-upload-wireframe">
                                        <div class="file-upload-icon">[📎]</div>
                                        <h3>[File Upload Area]</h3>
                                        <p>Drag & drop file atau klik untuk browse<br>
                                        <small>Format: PDF, JPG, PNG, DOC (Max: 10MB)</small></p>
                                    </div>
                                    <div class="form-text">Upload file pendukung seperti sketsa, referensi, atau dokumen lainnya</div>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">Prioritas</label>
                                    <select class="form-control-wireframe">
                                        <option>[Dropdown] Normal</option>
                                        <option>Tinggi</option>
                                        <option>Mendesak</option>
                                    </select>
                                    <div class="form-text">Tentukan prioritas penanganan revisi</div>
                                </div>
                                
                                <div style="margin-top: 30px;">
                                    <button type="submit" class="button-wireframe button-primary button-large">
                                        [📤] Ajukan Revisi
                                    </button>
                                    <button type="reset" class="button-wireframe button-large">
                                        [🔄] Reset Form
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Footer -->
            <footer class="footer">
                [Footer] Copyright © Client Portal 2025
            </footer>
        </div>
    </div>
</body>
</html>
