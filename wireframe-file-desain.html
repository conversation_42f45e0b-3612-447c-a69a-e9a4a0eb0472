<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>File Desain - Client Portal Wireframe</title>
    <style>
        /* WIREFRAME STYLES - Low Fidelity */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background-color: #ffffff;
            color: #333333;
            line-height: 1.4;
        }

        /* Layout Structure */
        #wrapper {
            display: flex;
            min-height: 100vh;
            border: 2px solid #000;
        }

        /* Sidebar - Wireframe Style */
        .sidebar {
            width: 200px;
            background: #f0f0f0;
            border-right: 2px solid #000;
            padding: 10px;
            position: fixed;
            height: 100vh;
        }

        .sidebar-brand {
            text-align: center;
            padding: 15px 0;
            border-bottom: 1px solid #000;
            margin-bottom: 15px;
            background: #e0e0e0;
        }

        .nav-item {
            list-style: none;
            margin: 5px 0;
        }

        .nav-link {
            display: block;
            padding: 10px;
            border: 1px solid #ccc;
            text-decoration: none;
            color: #333;
            background: #f8f8f8;
        }

        .nav-item.active .nav-link {
            background: #d0d0d0;
            border: 2px solid #000;
        }

        /* Content Area */
        #content-wrapper {
            flex: 1;
            margin-left: 200px;
            display: flex;
            flex-direction: column;
            border-left: 2px solid #000;
        }

        /* Topbar - Wireframe */
        .topbar {
            background: #f5f5f5;
            padding: 15px;
            border-bottom: 2px solid #000;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        /* Main Content */
        .container-fluid {
            padding: 20px;
            flex: 1;
        }

        .page-heading {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ccc;
        }

        .page-title {
            font-size: 24px;
            font-weight: bold;
        }

        .breadcrumb {
            font-size: 14px;
            color: #666;
        }

        /* Wireframe Components */
        .wireframe-box {
            border: 2px solid #000;
            background: #f9f9f9;
            margin-bottom: 20px;
            padding: 15px;
        }

        .wireframe-header {
            background: #e0e0e0;
            padding: 10px;
            border-bottom: 1px solid #000;
            font-weight: bold;
            margin: -15px -15px 15px -15px;
        }

        .alert-wireframe {
            border: 2px dashed #666;
            background: #f0f0f0;
            padding: 15px;
            margin-bottom: 20px;
        }

        /* Grid Layout */
        .row {
            display: flex;
            flex-wrap: wrap;
            margin: -10px;
        }

        .col-half {
            padding: 10px;
            flex: 0 0 50%;
        }

        .stat-wireframe {
            border: 2px solid #000;
            padding: 20px;
            background: #f8f8f8;
            text-align: center;
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            margin: 10px 0;
        }

        .stat-label {
            font-size: 12px;
            text-transform: uppercase;
            color: #666;
        }

        /* File Grid Wireframe */
        .file-grid-wireframe {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .file-card-wireframe {
            border: 2px solid #000;
            background: #f9f9f9;
        }

        .file-preview-wireframe {
            height: 150px;
            background: #e0e0e0;
            border-bottom: 1px solid #000;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            color: #999;
        }

        .file-info-wireframe {
            padding: 15px;
        }

        .file-name-wireframe {
            font-weight: bold;
            margin-bottom: 10px;
            border-bottom: 1px solid #ccc;
            padding-bottom: 5px;
        }

        .file-meta-wireframe {
            font-size: 12px;
            color: #666;
            margin-bottom: 15px;
        }

        .button-wireframe {
            display: inline-block;
            padding: 8px 15px;
            border: 2px solid #000;
            background: #f0f0f0;
            text-decoration: none;
            color: #000;
            margin-right: 10px;
            font-size: 12px;
        }

        .button-primary {
            background: #d0d0d0;
        }

        /* Footer */
        .footer {
            background: #f0f0f0;
            padding: 15px;
            text-align: center;
            border-top: 2px solid #000;
            margin-top: auto;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            #content-wrapper {
                margin-left: 0;
            }

            .col-half {
                flex: 0 0 100%;
            }

            .file-grid-wireframe {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div id="wrapper">
        <!-- Sidebar Wireframe -->
        <ul class="sidebar">
            <div class="sidebar-brand">
                [LOGO]<br>Client Portal
            </div>

            <li class="nav-item">
                <a class="nav-link" href="#dashboard">
                    [📊] Dashboard
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="#progress">
                    [📋] Progress Proyek
                </a>
            </li>

            <li class="nav-item active">
                <a class="nav-link" href="#files">
                    [🖼️] File Desain
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="#revision">
                    [✏️] Ajukan Revisi
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="#rab">
                    [🧮] Lihat RAB
                </a>
            </li>
        </ul>
        
        <!-- Content Wrapper -->
        <div id="content-wrapper">
            <!-- Topbar Wireframe -->
            <nav class="topbar">
                <div>
                    [☰ Menu Toggle]
                </div>
                <div>
                    [🔔 Notifications (3)] [👤 vickymosafan ▼]
                </div>
            </nav>

            <!-- Main Content -->
            <div class="container-fluid">
                <!-- Page Heading -->
                <div class="page-heading">
                    <h1 class="page-title">File Desain Disetujui</h1>
                    <div class="breadcrumb">
                        Dashboard > File Desain
                    </div>
                </div>

                <!-- Info Alert -->
                <div class="alert-wireframe">
                    [ℹ️] Informasi: Halaman ini menampilkan file desain yang sudah melalui proses verifikasi dan disetujui untuk proyek Anda.
                </div>

                <!-- File Statistics -->
                <div class="row">
                    <div class="col-half">
                        <div class="stat-wireframe">
                            <div class="stat-label">Total File Disetujui</div>
                            <div class="stat-number">15</div>
                            <div>[🖼️]</div>
                        </div>
                    </div>

                    <div class="col-half">
                        <div class="stat-wireframe">
                            <div class="stat-label">File Baru (7 Hari Terakhir)</div>
                            <div class="stat-number">3</div>
                            <div>[⏰]</div>
                        </div>
                    </div>
                </div>

                <!-- File Gallery -->
                <div class="wireframe-box">
                    <div class="wireframe-header">
                        [🖼️] Galeri File Desain
                    </div>
                    <div class="file-grid-wireframe">
                        <div class="file-card-wireframe">
                            <div class="file-preview-wireframe">[PDF]</div>
                            <div class="file-info-wireframe">
                                <div class="file-name-wireframe">blueprint_v2.pdf</div>
                                <div class="file-meta-wireframe">
                                    Ukuran: 2.5 MB<br>
                                    Disetujui: 14 Jan 2025
                                </div>
                                <div>
                                    <a href="#" class="button-wireframe button-primary">[Download]</a>
                                    <a href="#" class="button-wireframe">[Preview]</a>
                                </div>
                            </div>
                        </div>

                        <div class="file-card-wireframe">
                            <div class="file-preview-wireframe">[JPG]</div>
                            <div class="file-info-wireframe">
                                <div class="file-name-wireframe">denah_lantai1.jpg</div>
                                <div class="file-meta-wireframe">
                                    Ukuran: 1.8 MB<br>
                                    Disetujui: 13 Jan 2025
                                </div>
                                <div>
                                    <a href="#" class="button-wireframe button-primary">[Download]</a>
                                    <a href="#" class="button-wireframe">[Preview]</a>
                                </div>
                            </div>
                        </div>

                        <div class="file-card-wireframe">
                            <div class="file-preview-wireframe">[DWG]</div>
                            <div class="file-info-wireframe">
                                <div class="file-name-wireframe">struktur_pondasi.dwg</div>
                                <div class="file-meta-wireframe">
                                    Ukuran: 3.2 MB<br>
                                    Disetujui: 12 Jan 2025
                                </div>
                                <div>
                                    <a href="#" class="button-wireframe button-primary">[Download]</a>
                                    <a href="#" class="button-wireframe">[Preview]</a>
                                </div>
                            </div>
                        </div>

                        <div class="file-card-wireframe">
                            <div class="file-preview-wireframe">[PNG]</div>
                            <div class="file-info-wireframe">
                                <div class="file-name-wireframe">3d_render.png</div>
                                <div class="file-meta-wireframe">
                                    Ukuran: 4.1 MB<br>
                                    Disetujui: 11 Jan 2025
                                </div>
                                <div>
                                    <a href="#" class="button-wireframe button-primary">[Download]</a>
                                    <a href="#" class="button-wireframe">[Preview]</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <footer class="footer">
                [Footer] Copyright © Client Portal 2025
            </footer>
        </div>
    </div>
</body>
</html>
