<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>Progress Proyek - Wireframe</title>
    <style>
        /* WIREFRAME STYLES - Low Fidelity */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            background-color: #ffffff;
            color: #333333;
            line-height: 1.4;
        }
        
        /* Layout Structure */
        #wrapper {
            display: flex;
            min-height: 100vh;
            border: 3px solid #000;
        }
        
        /* Sidebar - Wireframe Style */
        .sidebar {
            width: 200px;
            background: #f0f0f0;
            border-right: 3px solid #000;
            padding: 10px;
            position: fixed;
            height: 100vh;
        }
        
        .sidebar-brand {
            text-align: center;
            padding: 15px 0;
            border: 2px solid #000;
            margin-bottom: 15px;
            background: #e0e0e0;
            font-weight: bold;
        }
        
        .nav-item {
            list-style: none;
            margin: 5px 0;
        }
        
        .nav-link {
            display: block;
            padding: 10px;
            border: 1px solid #666;
            text-decoration: none;
            color: #333;
            background: #f8f8f8;
        }
        
        .nav-item.active .nav-link {
            background: #d0d0d0;
            border: 2px solid #000;
            font-weight: bold;
        }
        
        /* Content Area */
        #content-wrapper {
            flex: 1;
            margin-left: 200px;
            display: flex;
            flex-direction: column;
        }
        
        /* Topbar - Wireframe */
        .topbar {
            background: #f5f5f5;
            padding: 15px;
            border-bottom: 3px solid #000;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        /* Main Content */
        .container-fluid {
            padding: 20px;
            flex: 1;
        }
        
        .page-heading {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #000;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: bold;
        }
        
        .breadcrumb {
            font-size: 14px;
            color: #666;
            border: 1px dashed #999;
            padding: 5px;
        }
        
        /* Wireframe Components */
        .wireframe-box {
            border: 2px solid #000;
            background: #f9f9f9;
            margin-bottom: 20px;
            padding: 15px;
        }
        
        .wireframe-header {
            background: #e0e0e0;
            padding: 10px;
            border: 1px solid #000;
            font-weight: bold;
            margin: -15px -15px 15px -15px;
        }
        
        .alert-wireframe {
            border: 2px dashed #666;
            background: #f0f0f0;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        /* Grid Layout */
        .row {
            display: flex;
            flex-wrap: wrap;
            margin: -10px;
            margin-bottom: 20px;
        }
        
        .col-two-thirds {
            padding: 10px;
            flex: 0 0 66.67%;
        }
        
        .col-one-third {
            padding: 10px;
            flex: 0 0 33.33%;
        }
        
        /* Progress Section */
        .progress-wireframe {
            margin: 15px 0;
        }
        
        .progress-bar-wireframe {
            height: 30px;
            border: 2px solid #000;
            background: #f0f0f0;
            position: relative;
        }
        
        .progress-fill {
            height: 100%;
            background: repeating-linear-gradient(
                45deg,
                #d0d0d0,
                #d0d0d0 10px,
                #e0e0e0 10px,
                #e0e0e0 20px
            );
            width: 67%;
            border-right: 2px solid #000;
        }
        
        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-weight: bold;
        }
        
        /* Stats Grid */
        .stats-grid {
            display: flex;
            text-align: center;
            gap: 15px;
            margin: 15px 0;
        }
        
        .stat-item {
            flex: 1;
            border: 2px solid #000;
            padding: 15px;
            background: #f8f8f8;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
        }
        
        /* Table Wireframe */
        .table-wireframe {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        .table-wireframe th,
        .table-wireframe td {
            padding: 10px;
            border: 1px solid #000;
            text-align: left;
        }
        
        .table-wireframe th {
            background: #e0e0e0;
            font-weight: bold;
        }
        
        .table-wireframe td {
            background: #f8f8f8;
        }
        
        .badge-wireframe {
            padding: 3px 8px;
            border: 1px solid #000;
            background: #e0e0e0;
            font-size: 11px;
            font-weight: bold;
        }
        
        .badge-success {
            background: #d0d0d0;
        }
        
        .badge-warning {
            background: #e8e8e8;
        }
        
        /* Footer */
        .footer {
            background: #f0f0f0;
            padding: 15px;
            text-align: center;
            border-top: 3px solid #000;
            margin-top: auto;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            #content-wrapper {
                margin-left: 0;
            }
            
            .col-two-thirds, .col-one-third {
                flex: 0 0 100%;
            }
            
            .stats-grid {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div id="wrapper">
        <!-- Sidebar Wireframe -->
        <ul class="sidebar">
            <div class="sidebar-brand">
                [LOGO]<br>Client Portal
            </div>
            
            <li class="nav-item">
                <a class="nav-link" href="#dashboard">
                    [📊] Dashboard
                </a>
            </li>
            
            <li class="nav-item active">
                <a class="nav-link" href="#progress">
                    [📋] Progress Proyek
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="#files">
                    [🖼️] File Desain
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="#revision">
                    [✏️] Ajukan Revisi
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="#rab">
                    [🧮] Lihat RAB
                </a>
            </li>
        </ul>
        
        <!-- Content Wrapper -->
        <div id="content-wrapper">
            <!-- Topbar Wireframe -->
            <nav class="topbar">
                <div>
                    [☰ Menu Toggle]
                </div>
                <div>
                    [🔔 Notifications (3)] [👤 vickymosafan ▼]
                </div>
            </nav>
            
            <!-- Main Content -->
            <div class="container-fluid">
                <!-- Page Heading -->
                <div class="page-heading">
                    <h1 class="page-title">Progress Proyek Anda</h1>
                    <div class="breadcrumb">
                        Dashboard > Progress Proyek
                    </div>
                </div>
                
                <!-- Info Alert -->
                <div class="alert-wireframe">
                    [ℹ️] Timeline menampilkan progress tugas proyek Anda yang telah disetujui.
                </div>
                
                <!-- Progress Summary -->
                <div class="wireframe-box">
                    <div class="wireframe-header">
                        [📈] Ringkasan Progress
                    </div>
                    <div class="row">
                        <div class="col-two-thirds">
                            <h3>Progress Keseluruhan: 66.7%</h3>
                            <div class="progress-wireframe">
                                <div class="progress-bar-wireframe">
                                    <div class="progress-fill"></div>
                                    <div class="progress-text">66.7%</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-one-third">
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <div class="stat-number">8</div>
                                    <div class="stat-label">Selesai</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">4</div>
                                    <div class="stat-label">Proses</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">0</div>
                                    <div class="stat-label">Batal</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Timeline Table -->
                <div class="wireframe-box">
                    <div class="wireframe-header">
                        [📋] Timeline Proyek Anda
                    </div>
                    <table class="table-wireframe">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Nama Kegiatan</th>
                                <th>Tanggal Mulai</th>
                                <th>Tanggal Selesai</th>
                                <th>Status</th>
                                <th>Progress</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>Desain Pondasi</td>
                                <td>01 Jan 2025</td>
                                <td>05 Jan 2025</td>
                                <td><span class="badge-wireframe badge-success">[Selesai]</span></td>
                                <td>100%</td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>Struktur Lantai 1</td>
                                <td>06 Jan 2025</td>
                                <td>15 Jan 2025</td>
                                <td><span class="badge-wireframe badge-success">[Selesai]</span></td>
                                <td>100%</td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td>Instalasi Listrik</td>
                                <td>16 Jan 2025</td>
                                <td>25 Jan 2025</td>
                                <td><span class="badge-wireframe badge-warning">[Proses]</span></td>
                                <td>60%</td>
                            </tr>
                            <tr>
                                <td>4</td>
                                <td>Finishing Interior</td>
                                <td>26 Jan 2025</td>
                                <td>10 Feb 2025</td>
                                <td><span class="badge-wireframe badge-warning">[Proses]</span></td>
                                <td>0%</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- Footer -->
            <footer class="footer">
                [Footer] Copyright © Client Portal 2025
            </footer>
        </div>
    </div>
</body>
</html>
