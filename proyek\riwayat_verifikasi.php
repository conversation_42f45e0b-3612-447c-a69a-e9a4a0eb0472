<?php
require_once '../includes/session_manager.php';
// Pastikan hanya 'proyek' atau 'admin' yang bisa melihat riwayat
check_session_auth(['proyek', 'admin']);
require_once '../koneksi.php';

$page_title = "Riwayat Verifikasi";
include 'includes/header/header.php';
?>

<?php include 'includes/sidebar/sidebar.php'; ?>

<div id="content-wrapper" class="d-flex flex-column">
    <div id="content">
        <?php include 'includes/topbar/topbar.php'; ?>
        <div class="container-fluid">

            <div class="d-sm-flex align-items-center justify-content-between mb-4">
                <h1 class="h3 mb-0 text-gray-800">Riwayat Verifikasi</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0 bg-transparent p-0">
                        <li class="breadcrumb-item"><a href="proyek.php">Dashboard</a></li>
                        <li class="breadcrumb-item active">Riwayat Verifikasi</li>
                    </ol>
                </nav>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary"><i class="fas fa-history mr-2"></i>Log Aktivitas Verifikasi</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                            <thead class="thead-dark">
                                <tr>
                                    <th class="text-center">No</th>
                                    <th>Tanggal</th>
                                    <th>Nama Item</th>
                                    <th>Untuk Client</th>
                                    <th class="text-center">Tipe</th>
                                    <th class="text-center">Status</th>
                                    <th>Catatan</th>
                                    <th>Diverifikasi Oleh</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                // 📝 Perubahan 2: Query diperbarui untuk mengambil nama client
                                $sql_log = mysqli_query($koneksi, "
                                    SELECT 
                                        vl.tanggal_verifikasi, vl.tipe, vl.status_baru, vl.catatan,
                                        p.nama_petugas AS nama_verifikator,
                                        COALESCE(tp.nama_kegiatan, fg.gambar) AS nama_item,
                                        cl.first_name AS client_first_name,
                                        cl.last_name AS client_last_name
                                    FROM 
                                        verifikasi_log vl
                                    LEFT JOIN 
                                        petugas p ON vl.verifikator_id = p.id_petugas
                                    LEFT JOIN 
                                        tugas_proyek tp ON vl.item_id = tp.id AND vl.tipe = 'tugas'
                                    LEFT JOIN 
                                        file_gambar fg ON vl.item_id = fg.id AND vl.tipe = 'file'
                                    LEFT JOIN 
                                        users cl ON cl.id = COALESCE(tp.client_id, fg.client_id)
                                    ORDER BY 
                                        vl.tanggal_verifikasi DESC
                                ");

                                if (!$sql_log) {
                                    echo '<tr><td colspan="8">Error: ' . mysqli_error($koneksi) . '</td></tr>';
                                } elseif (mysqli_num_rows($sql_log) > 0) {
                                    $no = 1;
                                    while ($log = mysqli_fetch_assoc($sql_log)) {
                                ?>
                                <tr>
                                    <td class="text-center"><?php echo $no++; ?></td>
                                    <td><?php echo date('d M Y, H:i', strtotime($log['tanggal_verifikasi'])); ?></td>
                                    <td class="font-weight-bold"><?php echo htmlspecialchars($log['nama_item'] ?? 'Item tidak ditemukan'); ?></td>
                                    
                                    <td>
                                        <?php if (!empty($log['client_first_name'])): ?>
                                            <?php echo htmlspecialchars($log['client_first_name'] . ' ' . $log['client_last_name']); ?>
                                        <?php else: ?>
                                            <span class="text-muted">N/A</span>
                                        <?php endif; ?>
                                    </td>

                                    <td class="text-center">
                                        <span class="badge badge-light"><?php echo ucfirst($log['tipe']); ?></span>
                                    </td>
                                    <td class="text-center">
                                        <?php if($log['status_baru'] == 'approved'): ?>
                                            <span class="badge badge-success">Disetujui</span>
                                        <?php else: ?>
                                            <span class="badge badge-danger">Ditolak</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($log['catatan']); ?></td>
                                    <td><?php echo htmlspecialchars($log['nama_verifikator'] ?? 'User tidak ditemukan'); ?></td>
                                </tr>
                                <?php 
                                    } 
                                } else { 
                                ?>
                                <tr>
                                    <td colspan="8" class="text-center py-4">Belum ada riwayat verifikasi.</td>
                                </tr>
                                <?php } ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

        </div>
    </div>
    
    <?php include 'includes/footer/footer.php'; ?>
</div>
</body>
</html>