<?php
require_once '../includes/session_manager.php';
// Admin dan Proyek bisa melihat halaman ini
check_session_auth(['admin', 'proyek']);
require_once '../koneksi.php';

$page_title = "Daftar Tugas Harian";
include 'includes/header/header.php';
?>

<?php include 'includes/sidebar/sidebar.php'; ?>

        <div id="content-wrapper" class="d-flex flex-column">
            <div id="content">
                <?php include 'includes/topbar/topbar.php'; ?>
                <div class="container-fluid">

                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <h1 class="h3 mb-0 text-gray-800">Daftar Tugas Harian</h1>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb mb-0 bg-transparent p-0">
                                <li class="breadcrumb-item"><a href="proyek.php">Dashboard</a></li>
                                <li class="breadcrumb-item active">Daftar Tugas</li>
                            </ol>
                        </nav>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle mr-2"></i>
                        Halaman ini menampilkan tugas yang sudah melalui proses verifikasi dan disetujui.
                    </div>

                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary"><i class="fas fa-tasks mr-2"></i>Tugas yang Disetujui</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th class="text-center">No</th>
                                            <th>Nama Tugas</th>
                                            <th>Client</th>
                                            <th>Deskripsi</th>
                                            <th class="text-center">Timeline Pengerjaan</th>
                                            <th class="text-center">Status</th>
                                            <th class="text-center">Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    <?php
                                    // 📝 Perubahan 2: Query mengambil tgl_mulai dan tgl_selesai
                                    $sql = mysqli_query($koneksi, "
                                        SELECT tp.*, u.first_name, u.last_name 
                                        FROM tugas_proyek tp
                                        LEFT JOIN users u ON tp.client_id = u.id
                                        WHERE tp.status_verifikasi = 'approved' ORDER BY tp.tgl_mulai DESC
                                    ");

                                    if (mysqli_num_rows($sql) > 0) {
                                        $no = 1;
                                        while ($data = mysqli_fetch_array($sql)) {
                                    ?>
                                        <tr>
                                            <td class="text-center align-middle"><?php echo $no++; ?></td>
                                            <td class="align-middle font-weight-bold"><?php echo htmlspecialchars($data['nama_kegiatan']); ?></td>
                                            <td class="align-middle">
                                                <?php if (!empty($data['first_name'])): ?>
                                                    <i class="fas fa-user mr-2 text-gray-400"></i>
                                                    <?php echo htmlspecialchars($data['first_name'] . ' ' . $data['last_name']); ?>
                                                <?php else: ?>
                                                    <span class="text-muted">N/A</span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="align-middle"><?php echo htmlspecialchars($data['deskripsi']); ?></td>
                                            
                                            <td class="text-center align-middle">
                                                <small>
                                                    <?php echo date('d M Y', strtotime($data['tgl_mulai'])); ?>
                                                    <br>s/d<br>
                                                    <?php echo date('d M Y', strtotime($data['tgl_selesai'])); ?>
                                                </small>
                                            </td>
                                            <td class="text-center align-middle">
                                                <?php
                                                $status = $data['status'];
                                                $badgeClass = 'secondary';
                                                $statusText = 'Belum Diatur';
                                                if ($status == 'proses') { $badgeClass = 'info'; $statusText = 'Proses'; }
                                                elseif ($status == 'selesai') { $badgeClass = 'success'; $statusText = 'Selesai'; }
                                                elseif ($status == 'batal') { $badgeClass = 'danger'; $statusText = 'Dibatalkan'; }
                                                ?>
                                                <span class="badge badge-<?php echo $badgeClass; ?> px-2 py-1"><?php echo $statusText; ?></span>
                                            </td>
                                            <td class="text-center align-middle">
                                                <button type="button" class="btn btn-sm btn-outline-primary" data-toggle="modal" data-target="#updateStatusModal<?php echo $data['id']; ?>" title="Ubah Status">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    <?php } } else { ?>
                                        <tr>
                                            <td colspan="7" class="text-center py-4">Belum ada tugas yang disetujui.</td>
                                        </tr>
                                    <?php } ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                </div>
            <?php
            // 📝 Perubahan 5: Query untuk modal juga diubah
            $sql_modal = mysqli_query($koneksi, "
                SELECT tp.*, u.first_name, u.last_name
                FROM tugas_proyek tp
                LEFT JOIN users u ON tp.client_id = u.id
                WHERE tp.status_verifikasi = 'approved'
            ");
            if (mysqli_num_rows($sql_modal) > 0) {
                while ($data_modal = mysqli_fetch_array($sql_modal)) {
                    $status_modal = $data_modal['status'];
            ?>
            <div class="modal fade" id="updateStatusModal<?php echo $data_modal['id']; ?>" tabindex="-1" role="dialog">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title"><i class="fas fa-edit mr-2"></i>Ubah Status Tugas</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <form action="update_tugas.php" method="POST">
                            <div class="modal-body">
                                <input type="hidden" name="id" value="<?php echo $data_modal['id']; ?>">
                                <div class="alert alert-info">
                                    <p class="mb-1"><strong>Tugas:</strong> <?php echo htmlspecialchars($data_modal['nama_kegiatan']); ?></p>
                                    <p class="mb-0"><strong>Client:</strong> <?php echo htmlspecialchars($data_modal['first_name'] . ' ' . $data_modal['last_name']); ?></p>
                                </div>
                                <div class="form-group">
                                    <label for="status<?php echo $data_modal['id']; ?>" class="font-weight-bold">Status Tugas:</label>
                                    <select name="status" id="status<?php echo $data_modal['id']; ?>" class="form-control" required>
                                        <option value="proses" <?php echo ($status_modal == 'proses') ? 'selected' : ''; ?>>Dalam Proses</option>
                                        <option value="selesai" <?php echo ($status_modal == 'selesai') ? 'selected' : ''; ?>>Selesai</option>
                                        <option value="batal" <?php echo ($status_modal == 'batal') ? 'selected' : ''; ?>>Dibatalkan</option>
                                    </select>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                                <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <?php } } ?>
            
            <?php include 'includes/footer/footer.php'; ?>
        </div>
        </div>
</body>
</html>