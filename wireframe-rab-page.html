<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>Lihat RAB - Wireframe</title>
    <style>
        /* WIREFRAME STYLES - Low Fidelity */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            background-color: #ffffff;
            color: #333333;
            line-height: 1.4;
        }
        
        /* Layout Structure */
        #wrapper {
            display: flex;
            min-height: 100vh;
            border: 3px solid #000;
        }
        
        /* Sidebar - Wireframe Style */
        .sidebar {
            width: 200px;
            background: #f0f0f0;
            border-right: 3px solid #000;
            padding: 10px;
            position: fixed;
            height: 100vh;
        }
        
        .sidebar-brand {
            text-align: center;
            padding: 15px 0;
            border: 2px solid #000;
            margin-bottom: 15px;
            background: #e0e0e0;
            font-weight: bold;
        }
        
        .nav-item {
            list-style: none;
            margin: 5px 0;
        }
        
        .nav-link {
            display: block;
            padding: 10px;
            border: 1px solid #666;
            text-decoration: none;
            color: #333;
            background: #f8f8f8;
        }
        
        .nav-item.active .nav-link {
            background: #d0d0d0;
            border: 2px solid #000;
            font-weight: bold;
        }
        
        /* Content Area */
        #content-wrapper {
            flex: 1;
            margin-left: 200px;
            display: flex;
            flex-direction: column;
        }
        
        /* Topbar - Wireframe */
        .topbar {
            background: #f5f5f5;
            padding: 15px;
            border-bottom: 3px solid #000;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        /* Main Content */
        .container-fluid {
            padding: 20px;
            flex: 1;
        }
        
        .page-heading {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #000;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: bold;
        }
        
        .breadcrumb {
            font-size: 14px;
            color: #666;
            border: 1px dashed #999;
            padding: 5px;
        }
        
        /* Wireframe Components */
        .wireframe-box {
            border: 2px solid #000;
            background: #f9f9f9;
            margin-bottom: 20px;
            padding: 15px;
        }
        
        .wireframe-header {
            background: #e0e0e0;
            padding: 10px;
            border: 1px solid #000;
            font-weight: bold;
            margin: -15px -15px 15px -15px;
        }
        
        .alert-wireframe {
            border: 2px dashed #666;
            background: #f0f0f0;
            padding: 15px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        /* Grid Layout */
        .row {
            display: flex;
            flex-wrap: wrap;
            margin: -10px;
            margin-bottom: 20px;
        }
        
        .col-quarter {
            padding: 10px;
            flex: 0 0 25%;
        }
        
        /* Stat Cards */
        .stat-wireframe {
            border: 2px solid #000;
            padding: 20px;
            background: #f8f8f8;
            text-align: center;
        }
        
        .stat-icon {
            font-size: 32px;
            margin-bottom: 10px;
            border: 1px dashed #666;
            padding: 10px;
            background: #f0f0f0;
        }
        
        .stat-number {
            font-size: 28px;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .stat-label {
            font-size: 12px;
            text-transform: uppercase;
            color: #666;
            border-top: 1px solid #ccc;
            padding-top: 5px;
        }
        
        /* Table Wireframe */
        .table-wireframe {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        .table-wireframe th,
        .table-wireframe td {
            padding: 10px;
            border: 1px solid #000;
            text-align: left;
        }
        
        .table-wireframe th {
            background: #e0e0e0;
            font-weight: bold;
        }
        
        .table-wireframe td {
            background: #f8f8f8;
        }
        
        .badge-wireframe {
            padding: 3px 8px;
            border: 1px solid #000;
            background: #e0e0e0;
            font-size: 11px;
            font-weight: bold;
        }
        
        .badge-success {
            background: #d0d0d0;
        }
        
        .badge-warning {
            background: #e8e8e8;
        }
        
        .badge-danger {
            background: #f0f0f0;
        }
        
        .button-wireframe {
            display: inline-block;
            padding: 8px 15px;
            border: 2px solid #000;
            background: #f0f0f0;
            text-decoration: none;
            color: #000;
            margin-right: 10px;
            font-size: 12px;
        }
        
        .button-primary {
            background: #d0d0d0;
        }
        
        /* Footer */
        .footer {
            background: #f0f0f0;
            padding: 15px;
            text-align: center;
            border-top: 3px solid #000;
            margin-top: auto;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            #content-wrapper {
                margin-left: 0;
            }
            
            .col-quarter {
                flex: 0 0 50%;
            }
        }
        
        @media (max-width: 576px) {
            .col-quarter {
                flex: 0 0 100%;
            }
        }
    </style>
</head>
<body>
    <div id="wrapper">
        <!-- Sidebar Wireframe -->
        <ul class="sidebar">
            <div class="sidebar-brand">
                [LOGO]<br>Client Portal
            </div>
            
            <li class="nav-item">
                <a class="nav-link" href="#dashboard">
                    [📊] Dashboard
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="#progress">
                    [📋] Progress Proyek
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="#files">
                    [🖼️] File Desain
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="#revision">
                    [✏️] Ajukan Revisi
                </a>
            </li>
            
            <li class="nav-item active">
                <a class="nav-link" href="#rab">
                    [🧮] Lihat RAB
                </a>
            </li>
        </ul>
        
        <!-- Content Wrapper -->
        <div id="content-wrapper">
            <!-- Topbar Wireframe -->
            <nav class="topbar">
                <div>
                    [☰ Menu Toggle]
                </div>
                <div>
                    [🔔 Notifications (3)] [👤 vickymosafan ▼]
                </div>
            </nav>
            
            <!-- Main Content -->
            <div class="container-fluid">
                <!-- Page Heading -->
                <div class="page-heading">
                    <h1 class="page-title">Rencana Anggaran Biaya (RAB)</h1>
                    <div class="breadcrumb">
                        Dashboard > Lihat RAB
                    </div>
                </div>
                
                <!-- Info Alert -->
                <div class="alert-wireframe">
                    <div style="margin-right: 15px; font-size: 32px; border: 1px solid #000; padding: 10px;">[ℹ️]</div>
                    <div>
                        <h3>Informasi RAB</h3>
                        <p>Berikut adalah daftar Rencana Anggaran Biaya (RAB) untuk proyek Anda. Anda dapat melihat detail breakdown biaya dan mengunduh RAB dalam format PDF.</p>
                    </div>
                </div>
                
                <!-- RAB Statistics -->
                <div class="row">
                    <div class="col-quarter">
                        <div class="stat-wireframe">
                            <div class="stat-icon">[🧮]</div>
                            <div class="stat-number">5</div>
                            <div class="stat-label">Total RAB</div>
                        </div>
                    </div>
                    
                    <div class="col-quarter">
                        <div class="stat-wireframe">
                            <div class="stat-icon">[✅]</div>
                            <div class="stat-number">3</div>
                            <div class="stat-label">RAB Disetujui</div>
                        </div>
                    </div>
                    
                    <div class="col-quarter">
                        <div class="stat-wireframe">
                            <div class="stat-icon">[❌]</div>
                            <div class="stat-number">1</div>
                            <div class="stat-label">RAB Ditolak</div>
                        </div>
                    </div>
                    
                    <div class="col-quarter">
                        <div class="stat-wireframe">
                            <div class="stat-icon">[📝]</div>
                            <div class="stat-number">1</div>
                            <div class="stat-label">RAB Draft</div>
                        </div>
                    </div>
                </div>
                
                <!-- RAB Table -->
                <div class="wireframe-box">
                    <div class="wireframe-header">
                        [📋] Daftar RAB Proyek
                    </div>
                    <table class="table-wireframe">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Nama RAB</th>
                                <th>Total Biaya</th>
                                <th>Tanggal Dibuat</th>
                                <th>Status</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>RAB Pondasi & Struktur</td>
                                <td>Rp 150,000,000</td>
                                <td>05 Jan 2025</td>
                                <td><span class="badge-wireframe badge-success">[Approved]</span></td>
                                <td>
                                    <a href="#" class="button-wireframe button-primary">[Download]</a>
                                    <a href="#" class="button-wireframe">[Detail]</a>
                                </td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>RAB Instalasi Listrik</td>
                                <td>Rp 75,000,000</td>
                                <td>10 Jan 2025</td>
                                <td><span class="badge-wireframe badge-success">[Approved]</span></td>
                                <td>
                                    <a href="#" class="button-wireframe button-primary">[Download]</a>
                                    <a href="#" class="button-wireframe">[Detail]</a>
                                </td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td>RAB Finishing Interior</td>
                                <td>Rp 120,000,000</td>
                                <td>12 Jan 2025</td>
                                <td><span class="badge-wireframe badge-warning">[Draft]</span></td>
                                <td>
                                    <a href="#" class="button-wireframe">[Detail]</a>
                                </td>
                            </tr>
                            <tr>
                                <td>4</td>
                                <td>RAB Landscape</td>
                                <td>Rp 50,000,000</td>
                                <td>15 Jan 2025</td>
                                <td><span class="badge-wireframe badge-danger">[Rejected]</span></td>
                                <td>
                                    <a href="#" class="button-wireframe">[Detail]</a>
                                </td>
                            </tr>
                            <tr>
                                <td>5</td>
                                <td>RAB Keseluruhan Proyek</td>
                                <td>Rp 395,000,000</td>
                                <td>16 Jan 2025</td>
                                <td><span class="badge-wireframe badge-success">[Approved]</span></td>
                                <td>
                                    <a href="#" class="button-wireframe button-primary">[Download]</a>
                                    <a href="#" class="button-wireframe">[Detail]</a>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- Footer -->
            <footer class="footer">
                [Footer] Copyright © Client Portal 2025
            </footer>
        </div>
    </div>
</body>
</html>
