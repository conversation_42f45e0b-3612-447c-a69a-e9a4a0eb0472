<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>Client Dashboard - Wireframe</title>
    <style>
        /* WIREFRAME STYLES - Low Fidelity */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            background-color: #ffffff;
            color: #333333;
            line-height: 1.4;
        }
        
        /* Layout Structure */
        #wrapper {
            display: flex;
            min-height: 100vh;
            border: 3px solid #000;
        }
        
        /* Sidebar - Wireframe Style */
        .sidebar {
            width: 200px;
            background: #f0f0f0;
            border-right: 3px solid #000;
            padding: 10px;
            position: fixed;
            height: 100vh;
        }
        
        .sidebar-brand {
            text-align: center;
            padding: 15px 0;
            border: 2px solid #000;
            margin-bottom: 15px;
            background: #e0e0e0;
            font-weight: bold;
        }
        
        .nav-item {
            list-style: none;
            margin: 5px 0;
        }
        
        .nav-link {
            display: block;
            padding: 10px;
            border: 1px solid #666;
            text-decoration: none;
            color: #333;
            background: #f8f8f8;
        }
        
        .nav-item.active .nav-link {
            background: #d0d0d0;
            border: 2px solid #000;
            font-weight: bold;
        }
        
        /* Content Area */
        #content-wrapper {
            flex: 1;
            margin-left: 200px;
            display: flex;
            flex-direction: column;
        }
        
        /* Topbar - Wireframe */
        .topbar {
            background: #f5f5f5;
            padding: 15px;
            border-bottom: 3px solid #000;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .topbar-left {
            border: 1px solid #666;
            padding: 5px 10px;
            background: #e8e8e8;
        }
        
        .topbar-right {
            display: flex;
            gap: 15px;
            align-items: center;
        }
        
        .notification-box {
            border: 1px solid #666;
            padding: 5px 10px;
            background: #e8e8e8;
        }
        
        .user-box {
            border: 1px solid #666;
            padding: 5px 10px;
            background: #e8e8e8;
        }
        
        /* Main Content */
        .container-fluid {
            padding: 20px;
            flex: 1;
        }
        
        .page-heading {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #000;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: bold;
        }
        
        .welcome-text {
            font-size: 14px;
            color: #666;
            border: 1px dashed #999;
            padding: 5px;
        }
        
        /* Wireframe Grid */
        .row {
            display: flex;
            flex-wrap: wrap;
            margin: -10px;
            margin-bottom: 20px;
        }
        
        .col-quarter {
            padding: 10px;
            flex: 0 0 25%;
        }
        
        .col-two-thirds {
            padding: 10px;
            flex: 0 0 66.67%;
        }
        
        .col-one-third {
            padding: 10px;
            flex: 0 0 33.33%;
        }
        
        .col-full {
            padding: 10px;
            flex: 0 0 100%;
        }
        
        /* Wireframe Components */
        .wireframe-box {
            border: 2px solid #000;
            background: #f9f9f9;
            padding: 15px;
            height: 100%;
        }
        
        .wireframe-header {
            background: #e0e0e0;
            padding: 10px;
            border: 1px solid #000;
            font-weight: bold;
            margin: -15px -15px 15px -15px;
        }
        
        /* Stat Cards */
        .stat-card {
            text-align: center;
            padding: 20px;
        }
        
        .stat-icon {
            font-size: 32px;
            margin-bottom: 10px;
            border: 1px dashed #666;
            padding: 10px;
            background: #f0f0f0;
        }
        
        .stat-number {
            font-size: 28px;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .stat-label {
            font-size: 12px;
            text-transform: uppercase;
            color: #666;
            border-top: 1px solid #ccc;
            padding-top: 5px;
        }
        
        /* Progress Section */
        .progress-wireframe {
            margin: 15px 0;
        }
        
        .progress-bar-wireframe {
            height: 30px;
            border: 2px solid #000;
            background: #f0f0f0;
            position: relative;
        }
        
        .progress-fill {
            height: 100%;
            background: repeating-linear-gradient(
                45deg,
                #d0d0d0,
                #d0d0d0 10px,
                #e0e0e0 10px,
                #e0e0e0 20px
            );
            width: 67%;
            border-right: 2px solid #000;
        }
        
        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-weight: bold;
        }
        
        /* Quick Access */
        .quick-access-item {
            border: 1px solid #666;
            padding: 15px;
            margin-bottom: 10px;
            background: #f8f8f8;
        }
        
        .quick-access-title {
            font-weight: bold;
            margin-bottom: 5px;
            border-bottom: 1px dashed #999;
            padding-bottom: 3px;
        }
        
        .quick-access-desc {
            font-size: 12px;
            color: #666;
        }
        
        /* Timeline */
        .timeline-item {
            display: flex;
            margin-bottom: 15px;
            padding: 10px;
            border: 1px solid #ccc;
            background: #f8f8f8;
        }
        
        .timeline-icon {
            width: 40px;
            height: 40px;
            border: 2px solid #000;
            background: #e0e0e0;
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .timeline-content {
            flex: 1;
        }
        
        .timeline-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .timeline-date {
            font-size: 12px;
            color: #666;
            border: 1px dashed #999;
            padding: 2px 5px;
            display: inline-block;
        }
        
        /* Footer */
        .footer {
            background: #f0f0f0;
            padding: 15px;
            text-align: center;
            border-top: 3px solid #000;
            margin-top: auto;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            #content-wrapper {
                margin-left: 0;
            }
            
            .col-quarter, .col-two-thirds, .col-one-third {
                flex: 0 0 100%;
            }
        }
        
        @media (max-width: 576px) {
            .col-quarter {
                flex: 0 0 100%;
            }
        }
    </style>
</head>
<body>
    <div id="wrapper">
        <!-- Sidebar Wireframe -->
        <ul class="sidebar">
            <div class="sidebar-brand">
                [LOGO]<br>Client Portal
            </div>
            
            <li class="nav-item active">
                <a class="nav-link" href="#dashboard">
                    [📊] Dashboard
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="#progress">
                    [📋] Progress Proyek
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="#files">
                    [🖼️] File Desain
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="#revision">
                    [✏️] Ajukan Revisi
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="#rab">
                    [🧮] Lihat RAB
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="#logout">
                    [🚪] Keluar
                </a>
            </li>
        </ul>
        
        <!-- Content Wrapper -->
        <div id="content-wrapper">
            <!-- Topbar Wireframe -->
            <nav class="topbar">
                <div class="topbar-left">
                    [☰ Menu Toggle]
                </div>
                <div class="topbar-right">
                    <div class="notification-box">
                        [🔔 Notifications (3)]
                    </div>
                    <div class="user-box">
                        [👤 vickymosafan] [▼]
                    </div>
                </div>
            </nav>
            
            <!-- Main Content -->
            <div class="container-fluid">
                <!-- Page Heading -->
                <div class="page-heading">
                    <h1 class="page-title">Dashboard Client</h1>
                    <div class="welcome-text">
                        Selamat datang, vickymosafan!
                    </div>
                </div>
                
                <!-- Stats Cards Row -->
                <div class="row">
                    <div class="col-quarter">
                        <div class="wireframe-box stat-card">
                            <div class="stat-icon">[📋]</div>
                            <div class="stat-number">12</div>
                            <div class="stat-label">Total Tugas Disetujui</div>
                        </div>
                    </div>
                    
                    <div class="col-quarter">
                        <div class="wireframe-box stat-card">
                            <div class="stat-icon">[✅]</div>
                            <div class="stat-number">8</div>
                            <div class="stat-label">Tugas Selesai</div>
                        </div>
                    </div>
                    
                    <div class="col-quarter">
                        <div class="wireframe-box stat-card">
                            <div class="stat-icon">[⏰]</div>
                            <div class="stat-number">4</div>
                            <div class="stat-label">Tugas Dalam Proses</div>
                        </div>
                    </div>
                    
                    <div class="col-quarter">
                        <div class="wireframe-box stat-card">
                            <div class="stat-icon">[🖼️]</div>
                            <div class="stat-number">15</div>
                            <div class="stat-label">File Desain Disetujui</div>
                        </div>
                    </div>
                </div>
                
                <!-- Progress and Quick Access Row -->
                <div class="row">
                    <div class="col-two-thirds">
                        <div class="wireframe-box">
                            <div class="wireframe-header">
                                [📈] Progress Proyek
                            </div>
                            <h3>Progress Keseluruhan: 66.7%</h3>
                            <div class="progress-wireframe">
                                <div class="progress-bar-wireframe">
                                    <div class="progress-fill"></div>
                                    <div class="progress-text">66.7% Selesai</div>
                                </div>
                            </div>
                            <p style="text-align: center; margin-top: 15px; border: 1px dashed #999; padding: 10px;">
                                8 dari 12 tugas telah selesai
                            </p>
                        </div>
                    </div>
                    
                    <div class="col-one-third">
                        <div class="wireframe-box">
                            <div class="wireframe-header">
                                [⚡] Akses Cepat
                            </div>
                            <div class="quick-access-item">
                                <div class="quick-access-title">[📊] Lihat Progress Proyek</div>
                                <div class="quick-access-desc">Lihat progress proyek dalam bentuk Gantt chart</div>
                            </div>
                            <div class="quick-access-item">
                                <div class="quick-access-title">[🖼️] File Desain</div>
                                <div class="quick-access-desc">Lihat dan download file desain yang disetujui</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Activities -->
                <div class="row">
                    <div class="col-full">
                        <div class="wireframe-box">
                            <div class="wireframe-header">
                                [🕒] Aktivitas Terbaru
                            </div>
                            <div class="timeline-item">
                                <div class="timeline-icon">T</div>
                                <div class="timeline-content">
                                    <div class="timeline-title">Tugas "Desain Lantai 1" disetujui</div>
                                    <div class="timeline-date">15 Jan 2025 14:30</div>
                                </div>
                            </div>
                            <div class="timeline-item">
                                <div class="timeline-icon">F</div>
                                <div class="timeline-content">
                                    <div class="timeline-title">File "blueprint_v2.pdf" disetujui</div>
                                    <div class="timeline-date">14 Jan 2025 09:15</div>
                                </div>
                            </div>
                            <div class="timeline-item">
                                <div class="timeline-icon">T</div>
                                <div class="timeline-content">
                                    <div class="timeline-title">Tugas "Struktur Pondasi" disetujui</div>
                                    <div class="timeline-date">13 Jan 2025 16:45</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Footer -->
            <footer class="footer">
                [Footer] Copyright © Client Portal 2025
            </footer>
        </div>
    </div>
</body>
</html>
